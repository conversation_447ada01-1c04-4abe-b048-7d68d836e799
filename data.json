{"title": "Step Up Auth Dashboard", "description": "Step Up Auth Dashboard", "widgets": [{"id": 6444835775393584, "definition": {"title": "Overview", "background_color": "vivid_blue", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 2302480908403028, "definition": {"title": "4xx request hits", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "default_zero(query1)"}], "conditional_formats": [{"palette": "white_on_green", "value": 0, "comparator": "<="}, {"palette": "white_on_yellow", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{!resource_name:get_/actuator*,http.status_code:4*,env:$env.value,service:$service.value}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": false, "precision": 0}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 1346044113611278, "definition": {"title": "5xx request hits", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "default_zero(query1)"}], "conditional_formats": [{"palette": "white_on_green", "value": 0, "comparator": "<="}, {"palette": "white_on_red", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{!resource_name:get_/actuator*,http.status_code:5*,env:$env.value,service:$service.value}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": false, "precision": 0}, "layout": {"x": 4, "y": 0, "width": 2, "height": 2}}, {"id": 5286949289220710, "definition": {"title": "5xx error rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "default_zero(query1) / default_zero(query2) * 100", "number_format": {"unit": {"label": "%", "type": "custom_unit_label"}}}], "conditional_formats": [{"palette": "white_on_green", "value": 0.2, "comparator": "<="}, {"palette": "white_on_yellow", "value": 0.2, "comparator": ">"}, {"comparator": ">", "value": 0.5, "palette": "white_on_red"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{!resource_name:get_/actuator*,http.status_code:5*,env:$env.value,service:$service.value}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}, {"query": "sum:trace.servlet.request.hits{!resource_name:get_/actuator*,env:$env.value,service:$service.value}.as_count()", "data_source": "metrics", "name": "query2", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 6, "y": 0, "width": 2, "height": 2}}, {"id": 29701822412420, "definition": {"title": "2xx request hits", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "default_zero(query1)"}], "conditional_formats": [{"palette": "white_on_green", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{!resource_name:get_/actuator*,http.status_code:2*,env:$env.value,service:$service.value}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": false, "precision": 0}, "layout": {"x": 8, "y": 0, "width": 2, "height": 2}}, {"id": 2415421358762612, "definition": {"title": "Latency > 500ms", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query10"}], "conditional_formats": [{"comparator": ">=", "palette": "white_on_yellow", "value": 5}, {"comparator": "<", "value": 5, "palette": "white_on_green"}], "response_format": "scalar", "queries": [{"query": "count(v: v>=0.5):trace.servlet.request{service:$service.value,env:$env.value}.as_count()", "data_source": "metrics", "name": "query10", "aggregator": "sum"}]}], "autoscale": true, "precision": 2}, "layout": {"x": 10, "y": 0, "width": 2, "height": 2}}, {"id": 5806819017071164, "definition": {"title": "Total request hits", "title_size": "16", "title_align": "left", "requests": [{"response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{!resource_name:get_/actuator*,env:$env.value,service:$service.value} by {http.status_code}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}], "formulas": [{"formula": "default_zero(query1)"}], "sort": {"order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}], "type": "sunburst", "legend": {"type": "inline"}}, "layout": {"x": 0, "y": 2, "width": 4, "height": 4}}, {"id": 655102810970908, "definition": {"title": "Traffic Tracking", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"alias": "2xx", "style": {"palette": "green", "palette_index": 4}, "formula": "query1"}, {"style": {"palette": "warm", "palette_index": 2}, "alias": "4xx", "formula": "query2"}, {"alias": "5xx", "style": {"palette": "warm", "palette_index": 4}, "formula": "query3"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:trace.servlet.request.hits.by_http_status{$env,$service,http.status_class:2xx}.as_count()"}, {"name": "query2", "data_source": "metrics", "query": "sum:trace.servlet.request.hits.by_http_status{$env,$service,http.status_class:4xx}.as_count()"}, {"name": "query3", "data_source": "metrics", "query": "sum:trace.servlet.request.hits.by_http_status{$env,$service,http.status_class:5xx}.as_count()"}], "response_format": "timeseries", "style": {"palette": "datadog16", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "markers": []}, "layout": {"x": 4, "y": 2, "width": 4, "height": 4}}, {"id": 7872315110607294, "definition": {"title": "Step up Monitoring", "type": "manage_status", "display_format": "countsAndList", "color_preference": "text", "hide_zero_counts": true, "show_status": true, "last_triggered_format": "relative", "query": "service:(otp-svc OR step-up-auth-svc OR facial-central-svc OR facial-recognition-svc OR bio-vault-svc) team:bhu<PERSON>", "sort": "status,asc", "count": 50, "start": 0, "summary_type": "monitors", "show_priority": false, "show_last_triggered": false}, "layout": {"x": 8, "y": 2, "width": 4, "height": 4}}]}, "layout": {"x": 0, "y": 0, "width": 12, "height": 7}}, {"id": 217655849680292, "definition": {"title": "APIs Statistic", "background_color": "vivid_green", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 3569637791110260, "definition": {"title": "Request hits per resource", "title_size": "16", "title_align": "left", "requests": [{"response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:$service.value,!resource_name:get_/actuator*} by {resource_name}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}], "formulas": [{"formula": "default_zero(query1)"}], "sort": {"order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}], "type": "sunburst", "legend": {"hide_percent": true, "type": "inline"}}, "layout": {"x": 0, "y": 0, "width": 5, "height": 4}}, {"id": 3965943652607870, "definition": {"title": "API resources", "title_size": "16", "title_align": "left", "type": "query_table", "requests": [{"response_format": "scalar", "queries": [{"query": "count:trace.servlet.request{!resource_name:get_/actuator*,env:$env.value,service:$service.value} by {resource_name}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}, {"query": "avg:trace.servlet.request{!resource_name:get_/actuator*,env:$env.value,service:$service.value} by {resource_name}", "data_source": "metrics", "name": "query2", "aggregator": "avg"}, {"query": "max:trace.servlet.request{!resource_name:get_/actuator*,env:$env.value,service:$service.value} by {resource_name}", "data_source": "metrics", "name": "query3", "aggregator": "max"}, {"query": "p95:trace.servlet.request{!resource_name:get_/actuator*,env:$env.value,service:$service.value} by {resource_name}", "data_source": "metrics", "name": "query8", "aggregator": "percentile"}, {"query": "sum:trace.servlet.request.errors{!resource_name:get_/actuator*,env:$env.value,service:$service.value} by {resource_name}.as_count()", "data_source": "metrics", "name": "query10", "aggregator": "sum"}], "sort": {"count": 500, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}, "formulas": [{"alias": "Requests", "formula": "query1"}, {"alias": "Average", "conditional_formats": [{"palette": "white_on_red", "value": 5, "comparator": ">"}], "formula": "query2"}, {"alias": "Max", "formula": "query3"}, {"alias": "P95", "formula": "query8"}, {"alias": "Errors", "conditional_formats": [{"palette": "white_on_red", "value": 0, "comparator": ">"}], "formula": "query10"}, {"alias": "Error Rate (%)", "conditional_formats": [{"palette": "green_on_white", "value": 0, "comparator": "<="}, {"palette": "yellow_on_white", "value": 0, "comparator": ">"}, {"palette": "red_on_white", "value": 10, "comparator": ">="}], "cell_display_mode": "number", "formula": "(query10 / query1) * 100"}]}], "has_search_bar": "auto"}, "layout": {"x": 5, "y": 0, "width": 7, "height": 4}}]}, "layout": {"x": 0, "y": 7, "width": 12, "height": 5}}, {"id": 5805386753818720, "definition": {"title": "Step Up Statistic", "background_color": "vivid_green", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 1535543442661738, "definition": {"title": "Init Step-Up Session", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"type": "custom_unit_label", "label": "req"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,resource_name:post_/internal/step-up/init,service:step-up-auth-svc}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 0, "y": 0, "width": 3, "height": 2}}, {"id": 2766978074981748, "definition": {"title": "Validate Step-Up Session", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,resource_name:post_/internal/step-up/validation,service:step-up-auth-svc}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 3, "y": 0, "width": 3, "height": 2}}, {"id": 3560434587414898, "definition": {"title": "Update Step-Up Session", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,resource_name:put_/internal/step-up,service:step-up-auth-svc}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 6, "y": 0, "width": 3, "height": 2}}, {"id": 1792620223195104, "definition": {"title": "Verify Step-Up Session", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,resource_name:post_/internal/step-up/verification,service:step-up-auth-svc}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 9, "y": 0, "width": 3, "height": 2}}, {"id": 4353295646968432, "definition": {"title": "Incomplete Session", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:stepup.session.count{step:initialized, env:$env.value}.as_count()", "aggregator": "sum"}, {"data_source": "metrics", "name": "query2", "query": "sum:stepup.session.count{step:verified ,env:$env.value}.as_count()", "aggregator": "sum"}], "formulas": [{"number_format": {"unit": {"type": "canonical_unit", "unit_name": "session"}}, "formula": "exclude_null(query1) - exclude_null(query2)"}], "conditional_formats": [{"comparator": ">", "value": 50, "palette": "white_on_yellow"}, {"comparator": ">", "value": 100, "palette": "white_on_red"}, {"comparator": "<=", "value": 50, "palette": "white_on_green"}]}], "autoscale": true, "precision": 2, "timeseries_background": {"yaxis": {"include_zero": false}, "type": "area"}}, "layout": {"x": 0, "y": 2, "width": 4, "height": 4}}, {"id": 5500185759698648, "definition": {"title": "Top Flow", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"queries": [{"data_source": "metrics", "name": "query1", "query": "sum:stepup.session.count{step:initialized, env:$env.value} by {flowname}.as_count()", "aggregator": "sum"}], "response_format": "scalar", "formulas": [{"number_format": {"unit": {"type": "canonical_unit", "unit_name": "session"}}, "formula": "exclude_null(query1)"}], "sort": {"count": 10, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}], "style": {"display": {"type": "stacked", "legend": "automatic"}, "scaling": "absolute"}}, "layout": {"x": 4, "y": 2, "width": 4, "height": 4}}, {"id": 2752328643158134, "definition": {"title": "Top Incomplete Flow", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"queries": [{"data_source": "metrics", "name": "query1", "query": "sum:stepup.session.count{env:$env.value , step:initialized ,env:$env.value} by {flowname}.as_count()", "aggregator": "sum"}, {"data_source": "metrics", "name": "query2", "query": "sum:stepup.session.count{env:$env.value ,step:verified} by {flowname}.as_count()", "aggregator": "sum"}], "response_format": "scalar", "conditional_formats": [{"comparator": ">", "value": 20, "palette": "white_on_yellow"}, {"comparator": ">", "value": 40, "palette": "white_on_red"}], "formulas": [{"number_format": {"unit": {"type": "canonical_unit", "unit_name": "session"}}, "formula": "exclude_null(query1) - exclude_null(query2)"}], "sort": {"count": 10, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}], "style": {"display": {"type": "stacked", "legend": "automatic"}, "scaling": "absolute"}}, "layout": {"x": 8, "y": 2, "width": 4, "height": 4}}]}, "layout": {"x": 0, "y": 12, "width": 12, "height": 7}}, {"id": 6457213118285212, "definition": {"title": "OTP Auth Factor", "background_color": "green", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 1281507830117820, "definition": {"title": "Generate OTP (Legacy)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:tp-otp-svc,resource_name:post_/intapi/otps}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 2328884250679619, "definition": {"title": "<PERSON>erify OTP (Legacy)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:tp-otp-svc,resource_name:post_/intapi/otps/verify}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 4, "y": 0, "width": 4, "height": 2}}, {"id": 4673731477060676, "definition": {"title": "Generate OTP (Step Up)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:tp-otp-svc,resource_name:post_/otp}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 8, "y": 0, "width": 4, "height": 2}}, {"id": 3971202760490166, "definition": {"title": "<PERSON>erify OTP (Step Up)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:tp-otp-svc,resource_name:post_/otp/verify}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 8, "y": 2, "width": 4, "height": 2}}, {"id": 5281507830117821, "definition": {"title": "Generate OTP (Step Up Non-login)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:tp-otp-svc,resource_name:post_/n-otp}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 0, "y": 2, "width": 4, "height": 2}}, {"id": 6281507830117822, "definition": {"title": "Verify OTP (Step Up Non-login)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:tp-otp-svc,resource_name:post_/n-otp/verify}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 4, "y": 2, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 19, "width": 12, "height": 5}}, {"id": 7457213118285213, "definition": {"title": "Facial Auth Factor", "background_color": "orange", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 7281507830117820, "definition": {"title": "Facial Central Token", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:facial-central-svc,resource_name:post_/p-facial-central/token}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 7328884250679619, "definition": {"title": "Facial Central Preparing", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:facial-central-svc,resource_name:post_/p-facial-central/preparing}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 4, "y": 0, "width": 4, "height": 2}}, {"id": 7673731477060676, "definition": {"title": "Facial Central Verify", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:facial-central-svc,resource_name:post_/p-facial-central/*/verify}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 8, "y": 0, "width": 4, "height": 2}}, {"id": 7971202760490166, "definition": {"title": "Facial Central Enrollment", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:facial-central-svc,resource_name:post_/p-facial-central/*/enrollment}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 8, "y": 2, "width": 4, "height": 2}}, {"id": 7281507830117821, "definition": {"title": "Facial Recognition Enrollment", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:facial-recognition-svc,resource_name:post_/facial-recognition/enrollment}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 0, "y": 2, "width": 4, "height": 2}}, {"id": 7281507830117822, "definition": {"title": "<PERSON><PERSON> Vault <PERSON>sets", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1 + query2"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,service:bio-vault-svc,resource_name:*_/bio-vault/assets*}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}, {"query": "sum:trace.servlet.request.hits{env:$env.value,service:bio-vault-svc,resource_name:*_/internal/bio-vault/assets*}.as_count()", "data_source": "metrics", "name": "query2", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 4, "y": 2, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 24, "width": 12, "height": 5}}, {"id": 8390596371247158, "definition": {"title": "<PERSON>ce Bio Auth Factor", "background_color": "pink", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 5960988342926670, "definition": {"title": "Verify device bio ", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"number_format": {"unit": {"label": "req", "type": "custom_unit_label"}}, "formula": "query1"}], "conditional_formats": [{"palette": "custom_text", "value": 0, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "sum:trace.servlet.request.hits{env:$env.value,resource_name:post_/passcode-verification/device-biometric/verify,service:passcode-verification-svc}.as_count()", "data_source": "metrics", "name": "query1", "aggregator": "sum"}]}], "autoscale": true, "precision": 0}, "layout": {"x": 0, "y": 0, "width": 12, "height": 2}}, {"id": 3932736744255312, "definition": {"title": "Step up auth and auth factor service error logs", "title_size": "16", "title_align": "left", "requests": [{"query": {"query_string": "env:$env.value (service:step-up-auth-svc OR service:tp-otp-svc OR service:passcode-verification-svc) status:error", "data_source": "logs_stream", "indexes": [], "storage": "hot"}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "host", "width": "auto"}, {"field": "service", "width": "auto"}, {"field": "content", "width": "compact"}], "response_format": "event_list"}], "type": "list_stream"}, "layout": {"x": 0, "y": 2, "width": 12, "height": 3}}]}, "layout": {"x": 0, "y": 29, "width": 12, "height": 6}}, {"id": 4486547631689253, "definition": {"title": "Step up and components ECS Service summary", "background_color": "vivid_green", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 8537119673909723, "definition": {"title": "Tasks Running Status", "title_size": "16", "title_align": "left", "type": "query_table", "requests": [{"response_format": "scalar", "queries": [{"query": "sum:aws.ecs.service.desired{(servicename:step-up-auth-svc OR servicename:tp-otp-svc OR servicename:passcode-verification-svc) AND env:$env.value} by {servicename,env}.rollup(max, 1)", "data_source": "metrics", "name": "query1", "aggregator": "last"}, {"query": "sum:aws.ecs.service.running{(servicename:step-up-auth-svc OR servicename:tp-otp-svc OR servicename:passcode-verification-svc) AND env:$env.value} by {servicename,env}.rollup(max, 1)", "data_source": "metrics", "name": "query2", "aggregator": "last"}, {"query": "sum:aws.ecs.service.pending{(servicename:step-up-auth-svc OR servicename:tp-otp-svc OR servicename:passcode-verification-svc) AND env:$env.value} by {servicename,env}.rollup(max, 1)", "data_source": "metrics", "name": "query3", "aggregator": "last"}, {"query": "avg:aws.ecs.service.cpuutilization{(servicename:step-up-auth-svc OR servicename:tp-otp-svc OR servicename:passcode-verification-svc) AND env:$env.value} by {servicename,env}.rollup(avg, 1)", "data_source": "metrics", "name": "query4", "aggregator": "last"}, {"query": "avg:aws.ecs.service.memory_utilization{(servicename:step-up-auth-svc OR servicename:tp-otp-svc OR servicename:passcode-verification-svc) AND env:$env.value} by {servicename,env}.rollup(avg, 1)", "data_source": "metrics", "name": "query5", "aggregator": "last"}], "sort": {"count": 500, "order_by": [{"type": "formula", "index": 2, "order": "desc"}]}, "formulas": [{"alias": "Desired", "formula": "query1"}, {"alias": "Running", "formula": "query2"}, {"alias": "Pending", "conditional_formats": [{"palette": "white_on_yellow", "value": 0, "comparator": ">"}], "formula": "query3"}, {"alias": "CPU", "formula": "query4"}, {"alias": "Memory", "formula": "query5"}]}], "has_search_bar": "auto"}, "layout": {"x": 0, "y": 0, "width": 6, "height": 4}}, {"id": 6363350288470464, "definition": {"title": "CPU Usage", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:ecs.fargate.cpu.percent{env:$env.value AND (service:tp-otp-svc OR service:step-up-auth-svc OR service:passcode-verification-svc)} by {env,service}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "cool", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 6, "y": 0, "width": 6, "height": 2}}, {"id": 5264237371056073, "definition": {"title": "Memory Usage", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:ecs.fargate.mem.usage{env:$env.value AND (ecs_container_name:step-up-auth-svc OR ecs_container_name:tp-otp-svc OR ecs_container_name:passcode-verification-svc)} by {container_name,env}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "warm", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 6, "y": 2, "width": 6, "height": 2}}, {"id": 1783133622545076, "definition": {"title": "Current running tasks", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.ecs.service.running{(servicename:step-up-auth-svc OR servicename:tp-otp-svc OR servicename:passcode-verification-svc) AND env:$env.value} by {service,env}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 4, "width": 12, "height": 2}}, {"id": 2498914156065230, "definition": {"title": "Step up auth svc ", "type": "trace_service", "env": "$env.value", "service": "step-up-auth-svc", "span_name": "http.request", "show_hits": true, "show_errors": true, "show_latency": true, "show_breakdown": true, "show_distribution": true, "show_resource_list": true, "size_format": "medium", "display_format": "two_column"}, "layout": {"x": 0, "y": 6, "width": 12, "height": 6}}]}, "layout": {"x": 0, "y": 35, "width": 12, "height": 13, "is_column_break": true}}, {"id": 2573421887773488, "definition": {"title": "RDS Cluster", "background_color": "vivid_green", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 5782132526177148, "definition": {"title": "CPU by instance (%)", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.rds.cpuutilization{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "area"}]}, "layout": {"x": 0, "y": 0, "width": 6, "height": 2}}, {"id": 6441265912094780, "definition": {"title": "Free memory", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.rds.freeable_memory{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "blue", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 6, "y": 0, "width": 6, "height": 2}}, {"id": 5633286300228176, "definition": {"title": "Read latency", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.rds.read_latency{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "red", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 2, "width": 6, "height": 2}}, {"id": 8663242038321646, "definition": {"title": "Write latency", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.rds.write_latency{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "cool", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 6, "y": 2, "width": 6, "height": 2}}, {"id": 8649535467149362, "definition": {"title": "Network throughput", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.rds.network_throughput{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 4, "width": 6, "height": 2}}, {"id": 6558212901986318, "definition": {"title": "Total connections each cluster", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"query": "avg:aws.rds.database_connections{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "orange", "line_type": "solid", "line_width": "normal"}, "display_type": "area"}]}, "layout": {"x": 6, "y": 4, "width": 6, "height": 2}}, {"id": 7891091537657382, "definition": {"title": "Other Information", "title_size": "16", "title_align": "left", "type": "query_table", "requests": [{"response_format": "scalar", "queries": [{"query": "avg:aws.rds.queries{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}.as_rate()", "data_source": "metrics", "name": "query1", "aggregator": "last"}, {"query": "avg:aws.rds.commit_latency{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query2", "aggregator": "last"}, {"query": "avg:aws.rds.insert_latency{application IN (common-db-cluster,central-db-cluster) AND (environment:$env.value OR tyme_environment:$env.value)} by {application,environment}", "data_source": "metrics", "name": "query3", "aggregator": "last"}], "formulas": [{"alias": "Rate of queries", "formula": "query1"}, {"alias": "Committed transaction latency", "formula": "query2"}, {"alias": "Insert query latency", "formula": "query3"}]}], "has_search_bar": "auto"}, "layout": {"x": 0, "y": 6, "width": 12, "height": 2}}]}, "layout": {"x": 0, "y": 48, "width": 12, "height": 9}}, {"id": 8895637345846820, "definition": {"title": "Dynamo DB", "background_color": "vivid_blue", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 2186270482231250, "definition": {"title": "Step up auth table items", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "query2"}], "response_format": "scalar", "queries": [{"query": "avg:aws.dynamodb.item_count{tablename:step-up-auth,env:$env.value}", "data_source": "metrics", "name": "query2", "aggregator": "last"}]}], "autoscale": true, "precision": 2}, "layout": {"x": 0, "y": 0, "width": 2, "height": 4}}, {"id": 8442940598803132, "definition": {"title": "Step up auth table size", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"formulas": [{"formula": "query2"}], "response_format": "scalar", "queries": [{"query": "avg:aws.dynamodb.table_size{tablename:step-up-auth,env:$env.value}", "data_source": "metrics", "name": "query2", "aggregator": "last"}]}], "autoscale": true, "precision": 2}, "layout": {"x": 2, "y": 0, "width": 2, "height": 4}}, {"id": 8662172888018870, "definition": {"title": "Latency", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "vertical", "legend_columns": ["value"], "type": "timeseries", "requests": [{"formulas": [{"alias": "request_latency", "formula": "autosmooth(query1)"}], "queries": [{"query": "avg:aws.dynamodb.successful_request_latency{(tablename:step-up-auth* OR tablename:passcode-verification) AND env:$env.value} by {tablename}", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"scale": "linear"}}, "layout": {"x": 4, "y": 0, "width": 8, "height": 2}}, {"id": 5810637972033478, "definition": {"title": "Traffic", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "vertical", "legend_columns": ["value"], "type": "timeseries", "requests": [{"formulas": [{"alias": "read", "formula": "query2"}, {"alias": "write", "formula": "query4"}], "queries": [{"query": "avg:aws.dynamodb.consumed_read_capacity_units{tablename:step-up-auth* OR tablename:passcode-verification* AND env:$env.value} by {tablename}", "data_source": "metrics", "name": "query2"}, {"query": "avg:aws.dynamodb.consumed_write_capacity_units{tablename:step-up-auth* OR tablename:passcode-verification* AND env:$env.value} by {tablename}", "data_source": "metrics", "name": "query4"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"scale": "linear"}}, "layout": {"x": 4, "y": 2, "width": 4, "height": 2}}, {"id": 6575946386235874, "definition": {"title": "Error Tracking", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"alias": "failed_request", "formula": "query1"}], "queries": [{"query": "sum:aws.dynamodb.conditional_check_failed_requests{tablename:passcode-verification* OR tablename:step-up-auth* AND env:$env.value} by {tablename}.as_count()", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 2, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 57, "width": 12, "height": 5}}], "template_variables": [{"name": "env", "prefix": "env", "available_values": ["dev", "stg", "prd", "tst"], "default": "${Environment}"}, {"name": "service", "prefix": "service", "available_values": ["step-up-auth-svc", "tp-otp-svc", "passcode-verification-svc"], "default": "step-up-auth-svc"}], "layout_type": "ordered", "notify_list": [], "reflow_type": "fixed", "tags": []}